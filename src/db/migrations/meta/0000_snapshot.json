{"version": "6", "dialect": "sqlite", "id": "9c152098-69a1-4742-93a5-5791ce286f5e", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"notes": {"name": "notes", "columns": {"id": {"name": "id", "type": "text(36)", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"notes_user_id_users_id_fk": {"name": "notes_user_id_users_id_fk", "tableFrom": "notes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text(36)", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_api_key_unique": {"name": "users_api_key_unique", "columns": ["api_key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}